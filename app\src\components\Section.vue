<script setup lang="ts">
import { computed } from "vue";
import Default from "./Sections/Default.vue";
import FullBarColor from "./Sections/FullBarColor.vue";
import ExperienceCard from "./Sections/ExperienceCard.vue";
import type { DefaultOptions } from "./Sections/types";

type Sections = "default" | "fullBarColor" | "cards";

interface FullBarColorConfig {
	title: string;
	color: string;
}

interface ExperienceCardConfig {
	title: string;
	company: string;
	date: string;
	description: string;
}

type SectionConfigMap = {
	default: DefaultOptions;
	fullBarColor: FullBarColorConfig;
	cards: ExperienceCardConfig;
};

interface SectionProps<T extends Sections = Sections> {
	id: string;
	type: T;
	sectionConfig: SectionConfigMap[T];
}

const props = defineProps<SectionProps>();

const componentMap = {
	default: Default,
	fullBarColor: FullBarColor,
	cards: ExperienceCard,
} satisfies Record<Sections, any>;

const CurrentComponent = computed(() => componentMap[props.type]);
</script>

<template>
  <div
    class="flex flex-col gap-4 h-full w-full items-center"
    :id="props.id"
  >
    <component
      :is="CurrentComponent"
      :options="props.sectionConfig"
    >
      <slot />
    </component>
  </div>
</template>
