{"files": [], "references": [{"path": "./app"}, {"path": "./server"}], "compilerOptions": {"target": "ESNext", "lib": ["ESNext"], "module": "Preserve", "moduleResolution": "bundler", "moduleDetection": "force", "jsx": "react-jsx", "allowImportingTsExtensions": true, "verbatimModuleSyntax": true, "strict": true, "skipLibCheck": true, "noUncheckedIndexedAccess": true, "noEmit": true, "types": ["bun-types"], "baseUrl": ".", "paths": {"@app/*": ["app/src/*"], "@server/*": ["server/src/*"]}}}